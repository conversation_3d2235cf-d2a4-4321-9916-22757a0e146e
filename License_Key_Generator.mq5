//+------------------------------------------------------------------+
//|                                        License_Key_Generator.mq5 |
//|                          MISAPE Trading License Key Generator     |
//|                              For Vendor Use Only                 |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MISAPE TRADING"
#property link      "https://misape-trading.com"
#property version   "1.00"
#property description "License Key Generator for MISAPE Trading Bot"
#property script_show_inputs

//+------------------------------------------------------------------+
//| Input Parameters                                                 |
//+------------------------------------------------------------------+
input group "=== Customer Information ==="
input long CustomerAccount = ********;      // Customer's MT5 account number
input string CustomerHardwareID = "ABC123"; // Customer's hardware fingerprint
input string CustomerName = "John Doe";     // Customer name (for records)

input group "=== License Configuration ==="
input int SubscriptionType = 1;             // 0=Trial, 1=Monthly, 2=Quarterly, 3=BiAnnual, 4=Annual, 5=Lifetime
input int DurationDays = 30;                // License duration in days
input bool GenerateMultipleKeys = false;    // Generate keys for multiple durations

//+------------------------------------------------------------------+
//| Subscription Type Enumeration                                   |
//+------------------------------------------------------------------+
enum ENUM_SUBSCRIPTION_TYPE {
    SUBSCRIPTION_TRIAL = 0,     // 7-day trial
    SUBSCRIPTION_MONTHLY = 1,   // 1 month subscription
    SUBSCRIPTION_QUARTERLY = 2, // 3 months subscription
    SUBSCRIPTION_BIANNUAL = 3,  // 6 months subscription
    SUBSCRIPTION_ANNUAL = 4,    // 1 year subscription
    SUBSCRIPTION_LIFETIME = 5   // Lifetime license
};

//+------------------------------------------------------------------+
//| Script Start Function                                           |
//+------------------------------------------------------------------+
void OnStart() {
    Print("=== MISAPE TRADING LICENSE KEY GENERATOR ===");
    Print("Customer: ", CustomerName);
    Print("Account: ", CustomerAccount);
    Print("Hardware ID: ", CustomerHardwareID);
    Print("===============================================");
    
    if(GenerateMultipleKeys) {
        GenerateAllSubscriptionTypes();
    } else {
        string license_key = GenerateLicenseKey(
            (ENUM_SUBSCRIPTION_TYPE)SubscriptionType,
            DurationDays,
            CustomerAccount,
            CustomerHardwareID
        );
        
        Print("Generated License Key: ", license_key);
        SaveLicenseRecord(license_key, CustomerName, CustomerAccount);
    }
    
    Print("===============================================");
    Print("Key generation completed successfully!");
}

//+------------------------------------------------------------------+
//| Generate License Key                                            |
//+------------------------------------------------------------------+
string GenerateLicenseKey(ENUM_SUBSCRIPTION_TYPE subscription_type, int duration_days, 
                         long customer_account, string customer_hardware) {
    
    datetime current_time = TimeCurrent();
    datetime expiry_date = current_time + duration_days * 24 * 3600;
    
    // Part 1: Subscription type (A=Trial, B=Monthly, C=Quarterly, etc.)
    string part1 = CharToString(65 + (int)subscription_type); // A, B, C, D, E, F
    part1 += IntegerToString((int)(customer_account % 10000), 4, '0');
    
    // Part 2: Encoded expiry date (days since 2022-01-01)
    int days_since_base = (int)((expiry_date - **********) / 86400);
    string part2 = IntegerToString(days_since_base % 100000, 5, '0');
    
    // Part 3: Hardware binding (first 5 chars of hardware fingerprint)
    string part3 = StringSubstr(customer_hardware + "00000", 0, 5);
    
    // Part 4: Simple checksum
    int checksum = (StringLen(part1) + StringLen(part2) + (int)customer_account) % 1000;
    string part4 = IntegerToString(checksum, 3, '0');
    
    // Combine parts
    string license_key = "MISAPE-" + part1 + "-" + part2 + "-" + part3 + "-" + part4;
    
    Print("=== LICENSE KEY DETAILS ===");
    Print("Subscription: ", GetSubscriptionName(subscription_type));
    Print("Duration: ", duration_days, " days");
    Print("Expires: ", TimeToString(expiry_date, TIME_DATE));
    Print("License Key: ", license_key);
    Print("===========================");
    
    return license_key;
}

//+------------------------------------------------------------------+
//| Generate Keys for All Subscription Types                        |
//+------------------------------------------------------------------+
void GenerateAllSubscriptionTypes() {
    Print("Generating keys for all subscription types...");
    Print("");
    
    // Trial (7 days)
    GenerateLicenseKey(SUBSCRIPTION_TRIAL, 7, CustomerAccount, CustomerHardwareID);
    Print("");
    
    // Monthly (30 days)
    GenerateLicenseKey(SUBSCRIPTION_MONTHLY, 30, CustomerAccount, CustomerHardwareID);
    Print("");
    
    // Quarterly (90 days)
    GenerateLicenseKey(SUBSCRIPTION_QUARTERLY, 90, CustomerAccount, CustomerHardwareID);
    Print("");
    
    // Bi-Annual (180 days)
    GenerateLicenseKey(SUBSCRIPTION_BIANNUAL, 180, CustomerAccount, CustomerHardwareID);
    Print("");
    
    // Annual (365 days)
    GenerateLicenseKey(SUBSCRIPTION_ANNUAL, 365, CustomerAccount, CustomerHardwareID);
    Print("");
    
    // Lifetime (10 years)
    GenerateLicenseKey(SUBSCRIPTION_LIFETIME, 3650, CustomerAccount, CustomerHardwareID);
}

//+------------------------------------------------------------------+
//| Get Subscription Type Name                                      |
//+------------------------------------------------------------------+
string GetSubscriptionName(ENUM_SUBSCRIPTION_TYPE type) {
    switch(type) {
        case SUBSCRIPTION_TRIAL: return "7-Day Trial";
        case SUBSCRIPTION_MONTHLY: return "Monthly (30 days)";
        case SUBSCRIPTION_QUARTERLY: return "Quarterly (90 days)";
        case SUBSCRIPTION_BIANNUAL: return "Bi-Annual (180 days)";
        case SUBSCRIPTION_ANNUAL: return "Annual (365 days)";
        case SUBSCRIPTION_LIFETIME: return "Lifetime (3650 days)";
        default: return "Unknown";
    }
}

//+------------------------------------------------------------------+
//| Save License Record to File                                     |
//+------------------------------------------------------------------+
void SaveLicenseRecord(string license_key, string customer_name, long customer_account) {
    string filename = "MISAPE_License_Records.txt";
    int file_handle = FileOpen(filename, FILE_WRITE | FILE_TXT | FILE_ANSI, '\t');
    
    if(file_handle != INVALID_HANDLE) {
        FileWrite(file_handle, "=== MISAPE LICENSE RECORD ===");
        FileWrite(file_handle, "Date Generated: " + TimeToString(TimeCurrent()));
        FileWrite(file_handle, "Customer Name: " + customer_name);
        FileWrite(file_handle, "Customer Account: " + IntegerToString(customer_account));
        FileWrite(file_handle, "Hardware ID: " + CustomerHardwareID);
        FileWrite(file_handle, "License Key: " + license_key);
        FileWrite(file_handle, "Subscription: " + GetSubscriptionName((ENUM_SUBSCRIPTION_TYPE)SubscriptionType));
        FileWrite(file_handle, "Duration: " + IntegerToString(DurationDays) + " days");
        FileWrite(file_handle, "=============================");
        FileWrite(file_handle, "");
        
        FileClose(file_handle);
        Print("License record saved to: ", filename);
    } else {
        Print("Error: Could not save license record to file");
    }
}

//+------------------------------------------------------------------+
//| Validate Generated License Key                                  |
//+------------------------------------------------------------------+
bool ValidateLicenseKey(string key) {
    // Basic format validation
    if(StringLen(key) != 29) return false;
    if(StringSubstr(key, 0, 7) != "MISAPE-") return false;
    if(StringGetCharacter(key, 13) != '-') return false;
    if(StringGetCharacter(key, 19) != '-') return false;
    if(StringGetCharacter(key, 25) != '-') return false;
    
    Print("License key format validation: PASSED");
    return true;
}
